{"name": "@elizaos/eliza-starter", "version": "0.1.1", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsup src/index.ts --format esm --dts", "start": "tsc && node --loader ts-node/esm src/index.ts", "clean": "./scripts/clean.sh", "start:service:all": "pm2 start pnpm --name=\"all\" --restart-delay=3000 --max-restarts=10 -- run start:all", "stop:service:all": "pm2 stop all"}, "dependencies": {"@chainlink/functions-toolkit": "^0.3.2", "@elizaos/adapter-postgres": "0.1.7", "@elizaos/adapter-sqlite": "0.1.7", "@elizaos/client-auto": "0.1.7", "@elizaos/client-direct": "0.1.7", "@elizaos/core": "0.1.7", "@elizaos/plugin-bootstrap": "0.1.7", "@elizaos/plugin-evm": "^0.1.8", "@elizaos/plugin-image-generation": "0.1.7", "@elizaos/plugin-node": "0.1.7", "@tavily/core": "0.0.2", "agent-twitter-client": "0.0.18", "amqplib": "0.10.5", "better-sqlite3": "11.5.0", "dotenv": "^16.4.7", "ethers": "v5", "fs": "0.0.1-security", "net": "1.0.2", "node-cache": "^5.1.2", "path": "0.12.7", "readline": "1.3.0", "url": "0.11.4", "viem": "^2.22.8", "ws": "8.18.0", "yargs": "17.7.2", "zod": "3.23.8"}, "engines": {"node": ">=22"}, "pnpm": {"overrides": {"onnxruntime-node": "1.20.0"}, "onlyBuiltDependencies": ["@elizaos/plugin-node", "bcrypto", "better-sqlite3", "bigint-buffer", "bufferutil", "canvas", "eccrypto", "es5-ext", "esbuild", "ffmpeg-static", "keccak", "node-llama-cpp", "onnxruntime-node", "protobufjs", "puppeteer", "secp256k1", "sharp", "utf-8-validate", "wtf_wikipedia", "youtube-dl-exec"]}, "devDependencies": {"ts-node": "10.9.2", "tsup": "8.3.5", "typescript": "5.6.3"}}