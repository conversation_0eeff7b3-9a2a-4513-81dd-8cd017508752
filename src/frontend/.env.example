# Required environment variables
GEMINI_API_KEY= #  GOOGLE GEMINI API Key
EVM_PRIVATE_KEY= # 0x${PRIVATE_KEY}. private key on evm chains. prefix with 0x. 
ETHEREUM_PROVIDER_AVALANCHEFUJI= # rpc urls to avalanche fuji
SUPABASE_API_KEY= #  SUPABASE API Key (public anon key)

OPENAI_API_KEY=sk-* # Not used in our demo. If you use this, please update all references to GEMINI_API_KEY with OPENAI_API_KEY

# For the Twitter Integration - your stretch assignment.
TWITTER_USERNAME="username"
TWITTER_PASSWORD="password"
TWITTER_EMAIL="<EMAIL>"
TWITTER_POLL_INTERVAL=30 # seconds
TWITTER_DRY_RUN=false
TWITTER_RETRY_LIMIT=5