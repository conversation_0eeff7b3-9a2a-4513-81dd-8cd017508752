graph TD
    subgraph "前端技术栈"
        A1[ElizaOS Framework]
        A2[TypeScript]
        A3[Node.js]
        A4[Twitter API]
    end
    
    subgraph "智能合约技术栈"
        B1[Solidity 0.8.24]
        B2[OpenZeppelin]
        B3[ERC1155]
        B4[Hardhat/Foundry]
    end
    
    subgraph "Chainlink服务"
        C1[Chainlink Functions]
        C2[Chainlink Price Feeds]
        C3[Chainlink DON]
        C4[CCIP Ready]
    end
    
    subgraph "数据存储"
        D1[Supabase PostgreSQL]
        D2[SQLite本地缓存]
        D3[IPFS元数据]
        D4[链上状态]
    end
    
    subgraph "部署网络"
        E1[Avalanche Fuji]
        E2[EVM兼容]
        E3[跨链准备]
        E4[测试网络]
    end
    
    subgraph "AI/ML技术"
        F1[自然语言处理]
        F2[参数提取]
        F3[智能路由]
        F4[错误处理]
    end
    
    A1 --> B1
    B1 --> C1
    C1 --> D1
    B1 --> E1
    A1 --> F1
    
    %% 样式
    classDef frontend fill:#e3f2fd
    classDef contract fill:#f3e5f5
    classDef chainlink fill:#e8f5e8
    classDef data fill:#fff3e0
    classDef network fill:#fce4ec
    classDef ai fill:#f1f8e9
    
    class A1,A2,A3,A4 frontend
    class B1,B2,B3,B4 contract
    class C1,C2,C3,C4 chainlink
    class D1,D2,D3,D4 data
    class E1,E2,E3,E4 network
    class F1,F2,F3,F4 ai